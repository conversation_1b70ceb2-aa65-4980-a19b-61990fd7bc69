---
applyTo: '**/*.ts'
---

# TypeScript Coding Best Practices

## TypeScript-Specific Guidelines

- Use type annotations for function parameters,
- Return types for methods are optional
- Avoid using `any`; use specific types or generics where possible.
- Use ES6+ features and syntax.

## Formatting

- Use semicolons consistently.
- Use single quotes for strings, except when escaping is easier with double quotes.

## Error Handling

- Avoid using bare `catch` clauses; specify the error type if possible.
- Ensure proper error handling in asynchronous code.

## Documentation

- Add JSDoc comments to all public classes, interfaces, functions, and methods.

## Testing

- Write unit tests for all functions and classes.
- Use Vitest
- Name test files and functions clearly.

## File and Directory Structure

- Use a clear and consistent file naming convention.
- Use lowercase with hyphens for file and folder names.

<!-- todo: expand on turbo repo and its structure -->

## Performance

- Profile and optimize only after identifying bottlenecks.
- Use built-in data structures and libraries where possible.

## Version Control

- Commit code with clear, concise messages.
- Do not commit secrets or large binary files.
