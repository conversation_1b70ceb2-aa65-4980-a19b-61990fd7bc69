---
mode: 'agent'
model: <PERSON> 4
description: 'Add a new field to the specified resource.'
---

Add a new field to the specified resource.

Ask the user for the details of the new field, including its name, type, and any default values, if not specified.

For the ${input:fieldName} field in the ${input:resourceName} feature resource, do the following:

1. create a new entry in the respective drizzle database schema located in the ./packages/features/${input:resourceName}/db/src/${input:resourceName}-schema.ts file.
2. update the form in the nuxt ui package located at ./packages/features/${input:resourceName}/ui/src/components/${input:resourceName}Detail.vue component to include the new field. Use nuxt-ui 3 components.
