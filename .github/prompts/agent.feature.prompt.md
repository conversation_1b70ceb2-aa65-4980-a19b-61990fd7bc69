---
mode: 'agent'
---

# Workflow

Think thoroughly before acting. Iterate until the feature is completely implemented - verify your changes handle all edge cases and test rigorously. Plan before each function call and reflect on outcomes. Don't rely solely on function calls without strategic thinking.

## High-Level Feature Development Strategy

1. Understand the requirements deeply. Carefully read the feature specification and think critically about what needs to be built.
2. If you do not understand the requirements, ask clarifying questions before proceeding.
3. Explore the codebase. Investigate relevant files, search for similar patterns, and understand the existing architecture.
4. Design the feature architecture. Plan how the new feature integrates with existing systems and follows established patterns.
5. Develop a clear, step-by-step implementation plan. Break down the feature into manageable, incremental components.
6. Implement incrementally. Build small, testable pieces that progressively complete the feature.
7. Test each component. Write and run tests after each implementation step to verify correctness.
8. Integrate and validate. Ensure the feature works end-to-end and integrates seamlessly with existing functionality.
9. Reflect and document comprehensively. After tests pass, think about the original requirements, write additional tests for edge cases, and ensure the feature is properly documented.

Refer to the detailed sections below for more information on each step.

## 1. Deeply Understand the Requirements

Carefully read the feature specification and think hard about the implementation approach before coding.

- Clarify the user story and acceptance criteria
- Understand the business value and user experience goals
- Identify any constraints or dependencies

## 2. Codebase Exploration and Architecture Analysis

- Explore relevant files and directories to understand existing patterns.
- Search for similar features or components that can serve as references.
- Read and understand the current architecture and coding conventions.
- Identify where the new feature should be integrated.
- Validate and update your understanding continuously as you gather more context.

## 3. Design the Feature Architecture

- Plan the overall architecture and data flow for the new feature.
- Design API endpoints, data models, and user interface components as needed.
- Consider how the feature integrates with existing systems and follows established patterns.
- Identify reusable components and shared utilities.
- Outline a specific, simple, and verifiable sequence of steps to implement the feature.
- Break down the implementation into small, incremental components.

## 4. Incremental Implementation

- Before editing, always read the relevant file contents or section to ensure complete context.
- Start with core functionality and build outward to supporting features.
- Make small, testable, incremental changes that logically follow from your design and plan.
- Follow established coding patterns and conventions in the codebase.
- Implement proper error handling and input validation as you build.

## 5. Testing Strategy

- Write tests for new functionality as you implement each component.
- Test both happy path scenarios and edge cases.
- Ensure proper unit test coverage for business logic.
- Write integration tests for API endpoints and component interactions.
- Use print statements, logs, or temporary code to verify implementation during development.
- Test the feature end-to-end to ensure it meets the original requirements.

## 6. Continuous Validation

- Run tests frequently during development.
- After each implementation step, verify functionality works as expected.
- If tests fail, analyze failures and adjust your implementation.
- Validate that the feature integrates properly with existing functionality.
- Ensure all tests pass before moving to the next component.

## 7. Final Integration and Documentation

- Confirm the feature meets all original requirements and acceptance criteria.
- Review your implementation for code quality, performance, and maintainability.
- Write or update documentation for the new feature.
- Ensure proper error handling and user feedback mechanisms are in place.
- Iterate until you are extremely confident the feature is complete, robust, and all tests pass.
