name: 'build staging'

on:
  push:
    branches:
      - staging

env:
  REGISTRY: registry.devsuccess.ch
  USERNAME: stack2

jobs:
  build_and_push:
    runs-on: ubuntu-latest
    environment: staging
    permissions:
      contents: read
      packages: write
    strategy:
      matrix:
        include:
          - image: registry.devsuccess.ch/sevspo/turbo-rent-admin-ui
            dockerfile: apps/admin-ui/Dockerfile
          - image: registry.devsuccess.ch/sevspo/turbo-rent-api
            dockerfile: apps/api/Dockerfile
    steps:
      - uses: actions/checkout@v4
      - uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ env.USERNAME }}
          password: ${{ secrets.REGISTRY_PASSWORD }}

      - name: Extract Docker metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ matrix.image }}
          tags: |
            latest
            staging
          labels: |
            org.opencontainers.image.source=${{ github.repository }}
            org.opencontainers.image.revision=${{ github.sha }}
            org.opencontainers.image.created=${{ github.event.head_commit.timestamp }}

      - name: Setup Docker Buildx
        uses: docker/setup-buildx-action@v3

      - uses: docker/build-push-action@v5
        with:
          context: .
          file: ${{ matrix.dockerfile }}
          push: true
          platforms: linux/arm64/v8
          build-args: |
            TURBO_TEAM=${{ vars.TURBO_TEAM }}
            TURBO_TOKEN=${{ secrets.TURBO_TOKEN }}
            NODE_ENV=production
            NUXT_SSR_API_URL=${{ vars.NUXT_SSR_API_URL }}
            NUXT_PUBLIC_API_URL=${{ vars.NUXT_PUBLIC_API_URL }}
          cache-from: type=gha,ref=${{ matrix.image }}-cache
          cache-to: type=gha,ref=${{ matrix.image }}-cache,mode=max
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}

  trigger_portainer:
    needs: build_and_push
    runs-on: ubuntu-latest
    environment: staging
    if: success()
    steps:
      - name: Trigger Portainer Webhook
        run: |
          curl -X POST "${{ secrets.PORTAINER_WEBHOOK_URL }}"
