# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules
.pnp
.pnp.js

# testing
coverage
/.nyc_output

# build outputs
.nuxt/
.output/
out/
**/build
**/dist

# logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# misc
.DS_Store
*.pem

# local env files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# temp directories
.temp
.tmp

# runtime data
pids
*.pid
*.seed
*.pid.lock

# diagnostic reports
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# turbo
.turbo

# vercel
.vercel
apps/api/dist/main.js
