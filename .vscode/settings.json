{"eslint.workingDirectories": [{"mode": "auto"}], "workbench.colorCustomizations": {"activityBar.activeBackground": "#55ead7", "activityBar.background": "#55ead7", "activityBar.foreground": "#15202b", "activityBar.inactiveForeground": "#15202b99", "activityBarBadge.background": "#d755ea", "activityBarBadge.foreground": "#15202b", "commandCenter.border": "#15202b99", "sash.hoverBorder": "#55ead7", "statusBar.background": "#28e5cc", "statusBar.foreground": "#15202b", "statusBarItem.hoverBackground": "#18c2ac", "statusBarItem.remoteBackground": "#28e5cc", "statusBarItem.remoteForeground": "#15202b", "titleBar.activeBackground": "#28e5cc", "titleBar.activeForeground": "#15202b", "titleBar.inactiveBackground": "#28e5cc99", "titleBar.inactiveForeground": "#15202b99"}, "peacock.color": "#28e5cc", "eslint.useFlatConfig": true, "github.copilot.chat.codesearch.enabled": true, "chat.promptFiles": true, "tailwindCSS.classAttributes": ["class", "ui"], "tailwindCSS.experimental.classRegex": [["ui:\\s*{([^)]*)\\s*}", "(?:'|\"|`)([^']*)(?:'|\"|`)"]]}