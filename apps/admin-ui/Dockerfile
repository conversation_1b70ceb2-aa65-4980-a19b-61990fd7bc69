FROM node:22-alpine AS base

RUN apk update
RUN apk add --no-cache libc6-compat

WORKDIR /app

ENV NODE_ENV=production
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
RUN corepack enable pnpm

# -------------------------------------- #

FROM base AS builder

RUN pnpm install --global turbo@^2.5
COPY . .
RUN turbo prune @turbo-rent/admin-ui --docker

# -------------------------------------- #

FROM base AS installer

# todo: what if we want to use for staging and production?
# todo: align env vars with caching tubo
ARG TURBO_TEAM
ENV TURBO_TEAM=$TURBO_TEAM
 
ARG TURBO_TOKEN
ENV TURBO_TOKEN=$TURBO_TOKEN

ARG NUXT_SSR_API_URL
ARG NUXT_PUBLIC_API_URL

#ENV NUXT_SSR_API_URL=${NUXT_SSR_API_URL}
#ENV NUXT_PUBLIC_API_URL=${NUXT_PUBLIC_API_URL}

COPY --from=builder /app/out/json/ .
RUN pnpm install --frozen-lockfile

COPY --from=builder /app/out/full/ .
RUN pnpm run build

# -------------------------------------- #

FROM base AS runner

RUN adduser -D nuxtuser
USER nuxtuser

COPY --chown=nuxtuser:nuxtuser --from=installer /app/apps/admin-ui/.output ./

EXPOSE 3000
ENV NUXT_HOST=0.0.0.0
ENV NUXT_PORT=3000

CMD ["node" ,"/app/server/index.mjs"]
