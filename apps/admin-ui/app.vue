<script setup lang="ts">
import type { NavigationMenuItem } from '@nuxt/ui';

const items = ref<NavigationMenuItem[][]>([
  [
    { label: 'Home', to: '/' },
    { label: 'Artikel', to: '/articles' },
  ],
]);
</script>

<template>
  <UApp>
    <header class="mb-8">
      <UNavigationMenu orientation="horizontal" :items="items" class="p-2" />
      <!-- divider in primary color -->
      <div class="border-t border-primary mt-2" />
    </header>
    <main>
      <UContainer>
        <NuxtPage />
      </UContainer>
    </main>
  </UApp>
</template>
