// https://nuxt.com/docs/api/configuration/nuxt-config

export default defineNuxtConfig({
  compatibilityDate: '2024-11-01',
  devtools: { enabled: true },

  extends: ['@turbo-rent/article-ui'],
  modules: ['@nuxt/eslint', '@nuxt/ui'],

  // debug: true,

  // this is a test

  ssr: false, // Disable server-side rendering for this module

  // vite: {
  //   optimizeDeps: {
  //     exclude: [
  //       'zod/v4',
  //       'drizzle-zod',
  //       'drizzle-orm/pg-core',
  //       '@paralleldrive/cuid2',
  //     ],
  //   },
  // },
});
