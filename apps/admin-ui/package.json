{"name": "@turbo-rent/admin-ui", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev --port 3020", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@nuxt/ui": "3.1.1", "@nuxt/eslint": "1.3.0", "@turbo-rent/article-ui": "workspace:*", "eslint": "^9.0.0", "nuxt": "^3.17.5", "typescript": "^5.6.3", "vue": "^3.5.17", "vue-router": "^4.5.1"}}