FROM node:22-alpine AS base

RUN apk update
RUN apk add --no-cache libc6-compat

WORKDIR /app

ENV NODE_ENV=production
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
RUN corepack enable pnpm
# Install pnpm globally so we can use it in the image for running push
# RUN corepack prepare pnpm@10.10.0 --activate 
# RUN npm install -g pnpm@10.10.0

# -------------------------------------- #

FROM base AS builder

RUN pnpm install --global turbo@^2.5
COPY . .
RUN turbo prune @turbo-rent/api --docker

# -------------------------------------- #

FROM base AS installer

# add build relevant envs here
ARG TURBO_TEAM
ENV TURBO_TEAM=$TURBO_TEAM
 
ARG TURBO_TOKEN
ENV TURBO_TOKEN=$TURBO_TOKEN

COPY --from=builder /app/out/json/ .
RUN pnpm install --frozen-lockfile

COPY --from=builder /app/out/full/ .
RUN pnpm run build

# -------------------------------------- #

FROM base AS runner

RUN adduser -D apiuser
USER apiuser

COPY --chown=apiuser:apiuser --from=installer /app ./

EXPOSE 6000

CMD ["node", "/app/apps/api/dist/main.js"]
#CMD ["pnpm", "start:prod"]
