{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "src", "compilerOptions": {"deleteOutDir": true, "webpack": true}, "projects": {"article-api": {"type": "library", "root": "../../packages/features/article/api", "entryFile": "index", "sourceRoot": "../../packages/features/article/api/src", "compilerOptions": {"tsConfigPath": "../../packages/features/article/api/tsconfig.json"}}, "db-provider": {"type": "library", "root": "../../packages/api/db-provider", "entryFile": "index", "sourceRoot": "../../packages/api/db-provider/src", "compilerOptions": {"tsConfigPath": "../../packages/api/db-provider/tsconfig.json"}}, "common": {"type": "library", "root": "../../packages/api/common", "entryFile": "index", "sourceRoot": "../../packages/api/common/src", "compilerOptions": {"tsConfigPath": "../../packages/api/common/tsconfig.json"}}}}