{"name": "@turbo-rent/api", "version": "0.0.0", "private": true, "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\" \"libs/**/*.ts\"", "start": "nest start", "dev": "nest start --tsc --watch", "dev:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/platform-express": "^11.0.1", "@turbo-rent/article-api": "workspace:*", "@turbo-rent/common": "workspace:*", "@turbo-rent/db-provider": "workspace:*", "docker-secret": "^2.0.6", "dotenv": "16.5.0", "dotenv-expand": "^12.0.2", "pg": "^8.15.6", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1"}, "devDependencies": {"@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@turbo-rent/eslint-config": "workspace:*", "@turbo-rent/tsconfig": "workspace:*", "@types/dotenv": "^8.2.0", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/node": "^22.10.7", "@types/pg": "^8.15.1", "@types/supertest": "^6.0.2", "eslint": "^9.18.0", "jest": "^29.7.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "./coverage", "testEnvironment": "node", "roots": ["<rootDir>/src/", "<rootDir>/libs/"], "moduleNameMapper": {"^@app/article-api(|/.*)$": "<rootDir>/libs/article-api/src/$1"}}}