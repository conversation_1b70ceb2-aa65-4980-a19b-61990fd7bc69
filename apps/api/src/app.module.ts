import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ArticleApiModule } from '@turbo-rent/article-api/article-api.module';
import { DbProviderModule } from '@turbo-rent/db-provider/db-provider.module';
import { ConfigModule } from '@nestjs/config';
import { readFileSync } from 'fs';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true, // Make the configuration available globally
      load: [
        () => ({
          environment: process.env.NODE_ENV || 'development',
          database: {
            // todo: check if trubo conform, check variables story!
            username: process.env.DATABASE_USERNAME,
            password: process.env.DATABASE_PASSWORD_FILE
              ? readFileSync(process.env.DATABASE_PASSWORD_FILE, 'utf8').trim()
              : process.env.DATABASE_PASSWORD,
            host: process.env.DATABASE_HOST,
            port: process.env.DATABASE_PORT,
            name: process.env.DATABASE_NAME,
          },
        }),
      ],
    }),
    DbProviderModule,
    ArticleApiModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
