import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const globalPrefix = 'api';
  app.setGlobalPrefix(globalPrefix);
  app.enableCors();

  // Set the port from environment variable or default to 6000
  const port =
    process.env.NODE_ENV === 'production' ? 6000 : process.env.API_PORT || 6000;
  await app.listen(port);
  console.log(`API is running on http://localhost:${port}/${globalPrefix}`);
}
bootstrap();
