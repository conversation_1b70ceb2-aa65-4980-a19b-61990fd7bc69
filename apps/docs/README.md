## Getting Started

First, run the development server:

```bash
pnpm dev
```

Open [http://localhost:3001](http://localhost:3001) with your browser to see the result.

You can start editing the page by modifying `app.vue`. The page auto-updates as you edit the file.

To create [API routes](https://nuxt.com/docs/guide/directory-structure/server), add an `api/` or a `routes` directory to the `server/` directory and create `your-file.ts` which will contain your api logic. Like `server/api/hello.ts` would map to [http://localhost:3001/api/hello](http://localhost:3001/api/hello).

> See the guide for more details -> [directory-structure/server](https://nuxt.com/docs/guide/directory-structure/server)

## Learn More

To learn more about Next.js, take a look at the following resources:

- [NuxtJs Official documentation](https://nuxt.com/docs/getting-started/introduction) - learn about Nuxt to create production-grade full-stack web apps and websites features and API.

## Deploy on Vercel

You can easily deploy your Nuxt app by using the [Vercel Platform](https://vercel.com/new?utm_source=github.com&utm_medium=referral&utm_campaign=turborepo-readme).

Check out our [Nuxt deployment documentation](https://vercel.com/docs/frameworks/nuxt) for more details.
