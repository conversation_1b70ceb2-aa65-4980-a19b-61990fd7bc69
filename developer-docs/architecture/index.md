# Architecture Documentation

This section describes the technical architecture and design decisions, as well as the technologies utilized for the project.

## Tech Stack

### Monorepo

| Technology                          | Context7 Repo ID  |
| ----------------------------------- | ----------------- |
| [Turborepo](https://turborepo.com/) | /vercel/turborepo |
| [pnpm](https://pnpm.io/)            | /pnpm/pnpm        |

### Frontend

| Technology                                    | Context7 Repo ID          |
| --------------------------------------------- | ------------------------- |
| [Nuxt 3](https://nuxt.com/)                   | /nuxt/nuxt                |
| [Vue 3](https://vuejs.org/)                   | /vuejs/vue                |
| [Nuxt UI 3](https://ui.nuxt.com/)             | /nuxt/ui                  |
| [TailwindCSS](https://tailwindcss.com/)       | /tailwindlabs/tailwindcss |
| [Pinia](https://pinia.vuejs.org/)             | /vuejs/pinia              |
| [TypeScript](https://www.typescriptlang.org/) | /microsoft/TypeScript     |

### Backend

| Technology                                | Context7 Repo ID          |
| ----------------------------------------- | ------------------------- |
| [NestJS](https://nestjs.com/)             | /nestjs/nest              |
| [Drizzle ORM](https://orm.drizzle.team/)  | /drizzle-team/drizzle-orm |
| [PostgreSQL](https://www.postgresql.org/) | /postgres/postgres        |
| [Zod](https://zod.dev/)                   | /colinhacks/zod           |

### Development & Testing

| Technology                            | Context7 Repo ID      |
| ------------------------------------- | --------------------- |
| [Jest](https://jestjs.io/)            | /jestjs/jest          |
| [Vitest](https://vitest.dev/)         | /vitest-dev/vitest    |
| [Playwright](https://playwright.dev/) | /microsoft/playwright |
| [ESLint](https://eslint.org/)         | /eslint/eslint        |
| [Prettier](https://prettier.io/)      | /prettier/prettier    |

## Architecture and Setup

### Project Structure

The monorepo is organized into three main directories following the recommended turborepo architecture:

```
turbo-rent/
├── apps/           # All applications
├── packages/       # Shared packages
└── developer-docs/ # Documentation
```

For more information about the apps refer to the [Features](../features/index.md) section of the developer-docs.

### Monorepo Setup

Lets go into more detail about the monorepo setup, which is package based. Each lib has its own `package.json`.

There are two types internal packages.

- buildable
- just-in-time

They all have the scope `@turbo-rent` in their name. Some packages are buildable, some are not.

### Packages (`packages/`)

The `packages` directory contains shared code organized by purpose:

#### Core Packages

- **`api/`** - Shared API utilities and common functionality

  - `common/` - Common utilities and helpers
  - `db-provider/` - Database connection and provider setup

- **`config/`** - Shared configuration packages

  - `eslint-config/` - Shared ESLint configuration
  - `tsconfig/` - Shared TypeScript configuration

- **`db/`** - Database-related packages

  - `base/` - Base database utilities
  - `drizzle/` - Drizzle ORM configuration and schemas
  - `run-push/` - Database migration utilities

- **`ui/`** - Shared UI components and design system
  - Built with Nuxt 3 and Nuxt UI 3
  - Provides reusable components across applications

#### Feature Packages (`packages/features/`)

Feature packages contain all code related to specific business domains, organized with the following structure:

- **`article/`** - Article management feature

  - `api/` - Article-related API endpoints and business logic
  - `db/` - Article database schemas and migrations
  - `ui/` - Article-related UI components and pages

- **`rental/`** - Rental management feature

  - `api/` - Rental-related API endpoints and business logic
  - `db/` - Rental database schemas and migrations
  - `ui/` - Rental-related UI components and pages

- **`user/`** - User management feature
  - `api/` - User-related API endpoints and business logic
  - `db/` - User database schemas and migrations
  - `ui/` - User-related UI components and pages

### Architecture Benefits

This feature-driven monorepo architecture provides several advantages:

1. **Code Colocation** - All code related to a specific feature (API, database, UI) is located in one place
2. **Shared Dependencies** - Common utilities and configurations are shared across the entire codebase
3. **Type Safety** - End-to-end TypeScript ensures type safety from database to UI
4. **Efficient Builds** - Turborepo's intelligent caching speeds up builds and tests
5. **Scalability** - New features can be added as self-contained packages
6. **Maintainability** - Clear separation of concerns makes the codebase easier to maintain

## Build System & Task Management

### Turborepo Configuration

The project uses Turborepo for orchestrating builds and tasks across the monorepo. Key configuration details:

- **Concurrency**: 20 parallel tasks maximum
- **Caching**: Intelligent caching based on file changes and dependencies
- **Task Dependencies**: Build tasks depend on upstream package builds (`^build`)

#### Available Tasks

- **`build`** - Builds all packages and applications

  - Outputs: `.nuxt/**`, `.output/**`, `dist/**`
  - Environment variables: Database config, ports, NODE_ENV

- **`dev`** - Starts development servers

  - Persistent processes with no caching
  - Depends on upstream builds

- **`lint`** - Runs ESLint across all packages

### Package Management

The project uses **pnpm workspaces** for dependency management:

```yaml
# pnpm-workspace.yaml
packages:
  - 'apps/*'
  - 'packages/*'
  - 'packages/config/*'
  - 'packages/api/*'
  - 'packages/db/*'
  - 'packages/features/article/*'
  - 'packages/features/rental/*'
  - 'packages/features/user/*'
```

#### Workspace Dependencies

Packages reference each other using workspace protocol:

- `"@turbo-rent/article-ui": "workspace:*"`
- `"@turbo-rent/common": "workspace:*"`
- `"@turbo-rent/db-provider": "workspace:*"`

## Development Environment

### Database Setup

The project includes Docker Compose configuration for local development:

```bash
# Start PostgreSQL database
pnpm db:up

# Stop database
pnpm db:down

# Stop and remove volumes
pnpm db:down:remove
```

### Development Servers

Each application runs on a different port:

- **API**: Default NestJS port
- **Admin UI**: Port 3020
- **Docs**: Configured port (see environment variables)

### Environment Variables

Key environment variables used across the monorepo:

- `NODE_ENV` - Environment (development/production)
- `DATABASE_URL` - PostgreSQL connection string
- `DATABASE_HOST`, `DATABASE_PORT`, `DATABASE_NAME` - Database connection details
- `DATABASE_USERNAME`, `DATABASE_PASSWORD` - Database credentials
- `API_PORT` - API server port
- `ADMIN_UI_PORT` - Admin UI port
- `DOCS_PORT` - Documentation site port

## Data Flow & Communication

### API Communication

- Frontend applications communicate with the NestJS API via HTTP/REST
- Shared types and schemas ensure type safety across the stack
- API endpoints are organized by feature domains

### Database Layer

- **Drizzle ORM** provides type-safe database access
- Database schemas are defined per feature in `packages/features/*/db/`
- Migrations and schema changes are managed through Drizzle

### State Management

- **Pinia** stores handle client-side state management
- Stores are organized by feature domain
- Server state is managed through API calls and caching
