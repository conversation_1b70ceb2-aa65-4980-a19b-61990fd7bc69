# Deployment Documentation

This section covers deployment and infrastructure setup for Turbo Rent.

## Deployment Options

- [Local Development](./local-development.md) - Running locally for development
- [Staging Deployment](./staging-deployment.md) - Staging environment setup
- [Production Deployment](./production-deployment.md) - Production environment setup
- [CI/CD Pipeline](./ci-cd-pipeline.md) - Automated deployment workflows

## Infrastructure

## Requirements

- Node.js runtime environment
- PostgreSQL database

## Environment Variables

Key environment variables required for deployment:

- `NODE_ENV`

# Monitoring and Maintenance
