# App Features

This section shall describe the different Applications and the features each shall have.

- Admin UI

  - Login
  - Logout
  - Dashboard
  - Users
  - Articles
  - Rentals
  - Billing
  - Reports
  - Email Notifications

- Customer UI
  - Login
  - Logout
  - Dashboard
  - Articles
  - Rentals

---

### Applications (`apps/`)

The `apps` directory contains the main user-facing applications:

- **`admin-ui/`** - Administrative interface built with Nuxt 3

  - Port: 3020 (development)
  - Technologies: Nuxt 3, Vue 3, Nuxt UI 3, TypeScript
  - Purpose: Administrative dashboard for managing the rental platform

- **`api/`** - Backend API server built with NestJS

  - Technologies: NestJS, TypeScript, PostgreSQL, Drizzle ORM
  - Purpose: RESTful API providing business logic and data access

- **`docs/`** - Documentation site built with Nuxt 3
  - Technologies: Nuxt 3, Vue 3, TypeScript
  - Purpose: User and developer documentation
