version: '3.9'
services:
  postgres:
    image: postgres:16
    container_name: pg-turbo-rent
    ports:
      - ${DATABASE_PORT}:5432
    env_file:
      - .env
    environment:
      POSTGRES_USER: ${DATABASE_USERNAME}
      POSTGRES_PASSWORD: ${DATABASE_PASSWORD}
      POSTGRES_DB: ${DATABASE_NAME}
    volumes:
      - postgres:/var/lib/postgresql/data
    networks:
      - turbo-rent-network

volumes:
  postgres:
    name: pg-turbo-rent

networks:
  turbo-rent-network:
    driver: bridge
