version: '3.9'

services:
  admin-ui:
    hostname: turbo-rent-admin-ui-staging
    image: registry.devsuccess.ch/sevspo/turbo-rent-admin-ui:staging
    environment:
      NODE_ENV: production
    networks:
      - traefik_public
      - turbo-rent-staging
    # Do not work!
    # healthcheck:
    #   test: ['CMD', 'curl', '-f', 'http://localhost:3000/']
    #   interval: 30s
    #   timeout: 10s
    #   retries: 3
    #   start_period: 40s
    deploy:
      restart_policy:
        condition: on-failure
        delay: 10s
        max_attempts: 3
        window: 60s
      placement:
        constraints:
          - node.role == worker
      labels:
        - 'traefik.enable=true'
        - 'traefik.http.routers.turbo-rent-admin-ui-staging.rule=Host(`turbo-rent.staging.devsuccess.ch`)'
        - 'traefik.http.routers.turbo-rent-admin-ui-staging.entrypoints=websecure'
        - 'traefik.http.routers.turbo-rent-admin-ui-staging.tls=true'
        - 'traefik.http.routers.turbo-rent-admin-ui-staging.tls.certresolver=letsencrypt'
        - 'traefik.http.services.turbo-rent-admin-ui-staging.loadbalancer.server.port=3000'

  api:
    hostname: turbo-rent-api-staging
    image: registry.devsuccess.ch/sevspo/turbo-rent-api:staging
    environment:
      NODE_ENV: production
      API_PORT: ${API_PORT}
      DATABASE_HOST: turbo-rent-postgres-staging
      DATABASE_PORT: ${DATABASE_PORT}
      DATABASE_NAME: ${DATABASE_NAME}
      DATABASE_USERNAME: ${DATABASE_USERNAME}
      DATABASE_PASSWORD_FILE: /run/secrets/turbo-rent-database-password
    secrets:
      - turbo-rent-database-password
    networks:
      - traefik_public
      - turbo-rent-staging
    # healthcheck:
    #   test: ['CMD', 'curl', '-f', 'http://localhost:6000/health']
    #   interval: 30s
    #   timeout: 10s
    #   retries: 3
    #   start_period: 40s
    deploy:
      restart_policy:
        condition: on-failure
        delay: 10s
        max_attempts: 3
        window: 60s
      placement:
        constraints:
          - node.role == worker
      labels:
        - 'traefik.enable=true'
        - 'traefik.http.routers.turbo-rent-api-staging.rule=Host(`turbo-rent.staging.devsuccess.ch`) && PathPrefix(`/api`)'
        - 'traefik.http.routers.turbo-rent-api-staging.entrypoints=websecure'
        - 'traefik.http.routers.turbo-rent-api-staging.tls=true'
        - 'traefik.http.routers.turbo-rent-api-staging.tls.certresolver=letsencrypt'
        - 'traefik.http.services.turbo-rent-api-staging.loadbalancer.server.port=6000'

  db:
    hostname: turbo-rent-postgres-staging
    image: postgres:16
    environment:
      POSTGRES_USER: ${DATABASE_USERNAME}
      POSTGRES_PASSWORD_FILE: /run/secrets/turbo-rent-database-password
      POSTGRES_DB: ${DATABASE_NAME}
    secrets:
      - turbo-rent-database-password
    volumes:
      - postgres-data-turbo-rent-staging:/var/lib/postgresql/data
    networks:
      - turbo-rent-staging
    deploy:
      restart_policy:
        condition: on-failure
        delay: 10s
        max_attempts: 3
        window: 60s
      placement:
        constraints:
          - node.role == worker

volumes:
  postgres-data-turbo-rent-staging:
    driver: local
    driver_opts:
      type: none
      device: /mnt/cephfs/turbo-rent-staging/postgres_data
      o: bind

secrets:
  turbo-rent-database-password:
    external: true

networks:
  turbo-rent-staging:
    driver: overlay
  traefik_public:
    external: true
