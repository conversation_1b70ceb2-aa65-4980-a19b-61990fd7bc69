{"name": "turbo-rent", "private": true, "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "db:up": "docker compose -f docker-compose-db.yml up -d", "db:down": "docker compose -f docker-compose-db.yml down", "db:down:remove": "docker compose -f docker-compose-db.yml down -v"}, "devDependencies": {"prettier": "^3.2.5", "turbo": "^2.5.2"}, "packageManager": "pnpm@10.13.0"}