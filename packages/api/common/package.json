{"name": "@turbo-rent/common", "private": true, "version": "0.0.0", "scripts": {"build": "tsc", "dev": "tsc --watch"}, "exports": {"./*": {"types": "./src/*.ts", "default": "./dist/*.js"}}, "devDependencies": {"@turbo-rent/eslint-config": "workspace:*", "@turbo-rent/tsconfig": "workspace:*", "@types/express": "^5.0.0", "eslint": "^9.18.0", "typescript": "^5.7.3"}, "dependencies": {"@nestjs/common": "^11.0.1", "reflect-metadata": "^0.2.2", "zod": "^3.25.67"}}