import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import type { Request, Response } from 'express';

@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();

    console.log(exception);

    const status =
      exception instanceof HttpException
        ? exception.getStatus()
        : HttpStatus.INTERNAL_SERVER_ERROR;

    const message =
      exception instanceof HttpException
        ? exception.getResponse()
        : 'Internal server error';

    response.status(status).json({
      statusCode: status,
      timestamp: new Date().toISOString(),
      path: request.url,
      message: message,
    });
  }
}

// @Catch() // <-- Catch all exceptions
// export class GlobalExceptionFilter extends BaseExceptionFilter {
//   constructor(
//     private readonly httpAdapter: HttpServer,
//     private readonly logger: LoggerService
//   ) {
//     super(httpAdapter);
//   }

//   private getHttpExceptionMessage(exception: HttpException): string {
//     const response = exception.getResponse();
//     // Example handling of custom response objects
//     if (typeof response === 'object' && 'message' in response) {
//       if (typeof response.message === 'string') return response.message;
//       if (
//         Array.isArray(response.message) &&
//         response.message.every((message) => typeof message === 'string')
//       )
//         return response.message.join('\n');
//     }
//     return typeof response === 'string' ? response : exception.message;
//   }

//   private logAndSendResponse(
//     statusCode: number,
//     message: string,
//     response: Response
//   ): void {
//     console.log(`Response/Error - Status: ${statusCode} - Message: ${message}`);
//     response.status(statusCode).json({ statusCode, message });
//   }

//   private handleUnexpectedError(error: Error, response: Response): void {
//     const context = error.stack ?? String(error);
//     this.logger.error(inspect(error), context); // <-- Log the error to your logger
//     // Sentry.captureException(error); // <-- Send the error to Sentry
//     this.logAndSendResponse(
//       HttpStatus.BAD_REQUEST,
//       'Please contact an administrator',
//       response
//     ); // <-- Send a generic error message to the client
//   }

//   // is there a drizzle error type?

//   private handleHttpException(
//     exception: HttpException,
//     response: Response
//   ): void {
//     const statusCode = exception.getStatus();
//     const message = this.getHttpExceptionMessage(exception);
//     this.logAndSendResponse(statusCode, message, response);
//   }

//   override catch(exception: unknown, host: ArgumentsHost) {
//     const ctx = host.switchToHttp();
//     const response = ctx.getResponse<Response>();

//     if (exception instanceof HttpException) {
//       this.handleHttpException(exception, response);
//       // ...add more custom error handlers here if needed
//     } else if (exception instanceof Error) {
//       // <-- Handle unexpected errors
//       this.handleUnexpectedError(exception, response);
//     } else {
//       // <-- Handle unexpected error types (this should never happen in practice!)
//       this.handleUnexpectedError(
//         new Error(`Unexpected error type, ${inspect(exception)}`),
//         response
//       );
//     }
//   }
// }
