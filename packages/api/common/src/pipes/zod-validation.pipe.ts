import {
  PipeTransform,
  ArgumentMetadata,
  BadRequestException,
} from '@nestjs/common';
import { ZodType } from 'zod/v4';

export class ZodValidationPipe implements PipeTransform {
  constructor(private schema: ZodType) {}

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  transform(value: unknown, metadata: ArgumentMetadata) {
    try {
      const parsedValue = this.schema.parse(value);
      return parsedValue;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      if (error && typeof error === 'object' && 'errors' in error) {
        throw new BadRequestException((error as { errors: unknown }).errors);
      }
      throw error;
    }

    // const parsedValue = this.schema.safeParse(value);
    // if (parsedValue.success) {
    //   return parsedValue.data;
    // }
    // throw new BadRequestException(parsedValue.error.format());
  }
}
