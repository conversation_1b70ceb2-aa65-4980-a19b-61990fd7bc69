import { Injectable, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  dataBaseFactory,
  type DrizzleDatabase,
} from '@turbo-rent/drizzle/database';
import { pushSchemaToDb } from '@turbo-rent/drizzle/push-schema';

@Injectable()
export class DbProviderService implements OnModuleInit {
  constructor(private configService: ConfigService) {}

  private _db: DrizzleDatabase | undefined;

  private getConnectionString(): string {
    const username = this.configService.get<string>('database.username');
    const password = this.configService.get<string>('database.password');
    const host = this.configService.get<string>('database.host');
    const port = this.configService.get<string>('database.port');
    const name = this.configService.get<string>('database.name');

    const missingParams = [];
    if (!username) missingParams.push('database.username');
    if (!password) missingParams.push('database.password');
    if (!host) missingParams.push('database.host');
    if (!port) missingParams.push('database.port');
    if (!name) missingParams.push('database.name');
    if (missingParams.length > 0) {
      throw new Error(
        `Database connection parameters are not set: ${missingParams.join(', ')}`,
      );
    }

    return `postgres://${username}:${password}@${host}:${port}/${name}`;
  }

  private async pushSchemaToDb() {
    const environment = this.configService.get<string>('environment');

    if (environment === 'production') {
      console.log('Trying to push schema to database...');
      // const connectionString = this.getConnectionString();
      await pushSchemaToDb()
        .then(() => {
          console.log('Schema pushed to database successfully');
        })
        .catch((error) => {
          console.error('Failed to push schema to database:', error);
          // todo: do like this in other places as well
          throw new Error('Failed to push schema to database', {
            cause: error,
          });
        });
    }
  }

  onModuleInit() {
    // await this.pushSchemaToDb();

    try {
      const connectionString = this.getConnectionString();
      this._db = dataBaseFactory(connectionString);

      // is this really a tested connection?
      console.log('Database connection established successfully');

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      if (error instanceof Error) {
        throw new Error(`Failed to connect to database: ${error.message}`);
      } else {
        throw new Error(`Failed to connect to database: ${error}`);
      }
    }
  }

  get db() {
    if (!this._db) {
      throw new Error('Database not initialized');
    }
    return this._db;
  }
}
