{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"target": "ES2022", "module": "NodeNext", "moduleResolution": "NodeNext", "composite": false, "incremental": false, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "inlineSources": false, "isolatedModules": true, "preserveWatchOutput": true, "skipLibCheck": true, "strict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noUncheckedIndexedAccess": true, "noImplicitOverride": true}, "exclude": ["node_modules"]}