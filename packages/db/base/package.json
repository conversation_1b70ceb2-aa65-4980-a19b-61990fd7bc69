{"name": "@turbo-rent/db-base", "private": true, "version": "0.0.0", "type": "module", "scripts": {"build": "tsc", "dev": "tsc --watch"}, "exports": {"./util": {"types": "./src/db-util.ts", "default": "./dist/db-util.js"}}, "dependencies": {"drizzle-orm": "^0.43.1"}, "devDependencies": {"@turbo-rent/tsconfig": "workspace:*", "@turbo-rent/eslint-config": "workspace:*", "eslint": "^9.18.0", "tsx": "^4.19.4", "typescript": "^5.7.3"}}