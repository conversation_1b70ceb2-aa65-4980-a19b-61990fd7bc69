{"name": "@turbo-rent/drizzle", "private": true, "version": "0.0.0", "type": "module", "scripts": {"build": "tsc", "dev": "tsc --watch", "drizzle:push": "pnpm exec drizzle-kit push --verbose --force --config ./src/drizzle.config.ts", "drizzle:generate": "pnpm exec drizzle-kit generate", "drizzle:migrate": "pnpm exec drizzle-kit migrate", "drizzle:studio": "pnpm exec drizzle-kit studio"}, "exports": {"./schema": {"types": "./src/schema.ts", "default": "./dist/schema.js"}, "./database": {"types": "./src/database.ts", "default": "./dist/database.js"}, "./relations": {"types": "./src/relations.ts", "default": "./dist/relations.js"}, "./queries/*": {"types": "./src/queries/*.ts", "default": "./dist/queries/*.js"}, "./dto/*": {"types": "./src/dto/*.ts", "default": "./dist/dto/*.js"}, "./push-schema": {"types": "./src/run-push-schema.ts", "default": "./dist/run-push-schema.js"}}, "devDependencies": {"@turbo-rent/tsconfig": "workspace:*", "@turbo-rent/eslint-config": "workspace:*", "@types/pg": "^8.15.1", "drizzle-kit": "0.31.1", "eslint": "^9.18.0", "tsx": "^4.19.4", "typescript": "^5.7.3", "@types/dotenv": "^8.2.0", "@types/node": "^20.6.0", "dotenv": "16.5.0", "dotenv-expand": "^12.0.2"}, "dependencies": {"@turbo-rent/article-db": "workspace:*", "drizzle-orm": "^0.43.1", "pg": "^8.15.6"}}