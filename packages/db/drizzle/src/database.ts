import { drizzle } from 'drizzle-orm/node-postgres';
import * as schema from './schema.js';
import * as relations from './relations.js';

export const dataBaseFactory = (connection: string) => {
  if (!connection) {
    throw new Error('Connection string for database is required');
  }

  return drizzle({
    connection: connection,
    schema: { ...schema, ...relations },
  });
};

// Extract the type from the function
export type DrizzleDatabase = ReturnType<typeof dataBaseFactory>;
