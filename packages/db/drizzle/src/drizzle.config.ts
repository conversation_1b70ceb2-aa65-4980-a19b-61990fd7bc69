import { config } from 'dotenv';
// import { expand } from 'dotenv-expand';
import { defineConfig } from 'drizzle-kit';
import { readFileSync } from 'fs';

// this works as well
// todo: remove expand, if not needed
config();

const getDataBaseUrl = () => {
  // todo: imporve handling of missing env variables, with zod?
  // revome the expand package?
  const databasePassword = process.env.DATABASE_PASSWORD_FILE
    ? readFileSync(process.env.DATABASE_PASSWORD_FILE, 'utf8').trim()
    : process.env.DATABASE_PASSWORD;

  const dbUrl = `postgres://${process.env.DATABASE_USERNAME}:${databasePassword}@${process.env.DATABASE_HOST}:${process.env.DATABASE_PORT}/${process.env.DATABASE_NAME}`;

  return dbUrl;
};

export default defineConfig({
  schema: ['./dist/schema.js', './dist/relations.js'],
  out: './src/migrations',
  dialect: 'postgresql',
  dbCredentials: {
    url: getDataBaseUrl(),
  },
  verbose: true,
  strict: true,
});
