import type {
  createArticle,
  getArticle,
  listArticles,
  setArticleCategory,
  updateArticle,
} from '../queries/article.js';

export type ListArticles = Awaited<ReturnType<typeof listArticles>>;

export type GetArticle = Awaited<ReturnType<typeof getArticle>>;

export type CreateArticle = Awaited<ReturnType<typeof createArticle>>;

export type UpdateArticle = Awaited<ReturnType<typeof updateArticle>>;

export type SetArticleCategoryResponse = Awaited<
  ReturnType<typeof setArticleCategory>
>;
