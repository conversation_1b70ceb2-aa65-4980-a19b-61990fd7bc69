import type {
  ArticleCreateDto,
  ArticleUpdateDto,
} from '@turbo-rent/article-db/dto';
import { eq } from 'drizzle-orm';
import { article } from '../schema.js';
import type { DrizzleDatabase } from '../database.js';
import { articleToCategory } from '../relations/article.js';

export const listArticles = async (db: DrizzleDatabase) => {
  return db.query.article.findMany({
    with: { categories: { with: { category: { columns: { id: true } } } } },
  });
  // return db.query.article.findMany();
};

export const getArticle = async (id: string, db: DrizzleDatabase) => {
  return db.query.article.findFirst({
    where: eq(article.id, id),
    with: { categories: { with: { category: { columns: { id: true } } } } },
  });
};

export const createArticle = async (
  data: ArticleCreateDto,
  db: DrizzleDatabase,
) => {
  return db.insert(article).values(data).returning();
};

export const updateArticle = async (
  id: string,
  data: ArticleUpdateDto,
  db: DrizzleDatabase,
) => {
  return db
    .update(article)
    .set({ ...data, updatedAt: new Date() })
    .where(eq(article.id, id))
    .returning();
};

export const setArticleCategory = async (
  data: {
    articleId: string;
    categoryId: string;
  },
  db: DrizzleDatabase,
) => {
  return db.insert(articleToCategory).values(data).returning();
};
