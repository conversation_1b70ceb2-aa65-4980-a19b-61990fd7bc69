import { article, category } from '@turbo-rent/article-db/schema';
import { relations } from 'drizzle-orm';
import { pgTable, primaryKey, text } from 'drizzle-orm/pg-core';

export const articleRelations = relations(article, ({ many }) => ({
  categories: many(articleToCategory),
}));

export const categoryRelations = relations(category, ({ many }) => ({
  articles: many(articleToCategory),
}));

// Relational Table
export const articleToCategory = pgTable(
  'articleToCategory',
  {
    articleId: text()
      .notNull()
      .references(() => article.id),
    categoryId: text()
      .notNull()
      .references(() => category.id),
  },
  (t) => [primaryKey({ columns: [t.articleId, t.categoryId] })],
);

export const articleToCategoryRelation = relations(
  articleToCategory,
  ({ one }) => ({
    article: one(article, {
      fields: [articleToCategory.articleId],
      references: [article.id],
    }),
    category: one(category, {
      fields: [articleToCategory.categoryId],
      references: [category.id],
    }),
  }),
);
