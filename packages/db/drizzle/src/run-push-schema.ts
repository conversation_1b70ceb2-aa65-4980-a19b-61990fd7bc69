import { spawn } from 'child_process';
import { dirname } from 'node:path';
import { fileURLToPath } from 'node:url';

export const pushSchemaToDb = async () => {
  return new Promise<void>((resolve, reject) => {
    const proc = spawn(
      'npx',
      [
        'drizzle-kit',
        'push',
        '--verbose',
        '--force',
        '--config',
        './src/drizzle.config.ts',
      ],
      {
        cwd: dirname(fileURLToPath(import.meta.url)),
        stdio: 'inherit', // stream output directly to console
        shell: true,
      },
    );

    proc.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`drizzle-kit push exited with code ${code}`));
      }
    });

    proc.on('error', (err) => {
      reject(err);
    });
  });
};
