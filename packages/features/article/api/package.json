{"name": "@turbo-rent/article-api", "private": true, "version": "0.0.0", "scripts": {"build": "tsc", "dev": "tsc --watch"}, "exports": {"./*": {"types": "./src/*.ts", "default": "./dist/*.js"}}, "devDependencies": {"@turbo-rent/tsconfig": "workspace:*", "@turbo-rent/eslint-config": "workspace:*", "eslint": "^9.18.0", "typescript": "^5.7.3"}, "dependencies": {"@turbo-rent/common": "workspace:*", "@turbo-rent/drizzle": "workspace:*", "@turbo-rent/db-provider": "workspace:*", "@turbo-rent/article-db": "workspace:*", "@nestjs/common": "^11.0.1", "reflect-metadata": "^0.2.2"}}