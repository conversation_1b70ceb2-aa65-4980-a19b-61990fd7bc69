import {
  Body,
  Controller,
  Get,
  Param,
  Patch,
  Post,
  UsePipes,
} from '@nestjs/common';
import {
  ArticleCreateSchema,
  type ArticleCreateDto,
} from '@turbo-rent/article-db/dto';
import { ZodValidationPipe } from '@turbo-rent/common/pipes/zod-validation.pipe';
import { ArticleApiService } from './article-api.service';

@Controller('article')
export class ArticleApiController {
  constructor(private articleApiService: ArticleApiService) {}

  @Get(':id')
  async getArticle(@Param('id') id: string) {
    return this.articleApiService.getArticle(id);
  }

  @Post('list')
  async listArticles() {
    return this.articleApiService.listArticles();
  }

  @Post()
  @UsePipes(new ZodValidationPipe(ArticleCreateSchema))
  async createArticle(@Body() data: ArticleCreateDto) {
    return this.articleApiService.createArticle(data);
  }

  @Patch(':id')
  async updateArticle(
    @Body(new ZodValidationPipe(ArticleCreateSchema)) data: ArticleCreateDto,
    @Param('id') id: string,
  ) {
    return this.articleApiService.updateArticle(id, data);
  }

  @Post('setArticleCategory')
  async setArticleCategory(
    @Body() data: { articleId: string; categoryId: string },
  ) {
    return this.articleApiService.setArticleCategory(data);
  }
}
