import { Injectable } from '@nestjs/common';
import { ArticleCreateDto, ArticleUpdateDto } from '@turbo-rent/article-db/dto';
import { DbProviderService } from '@turbo-rent/db-provider/db-provider.service';
import {
  createArticle,
  getArticle,
  listArticles,
  setArticleCategory,
  updateArticle,
} from '@turbo-rent/drizzle/queries/article';

@Injectable()
export class ArticleApiService {
  constructor(private drizzle: DbProviderService) {}

  async getArticle(id: string) {
    return getArticle(id, this.drizzle.db);
  }

  async listArticles() {
    return listArticles(this.drizzle.db);
  }

  async createArticle(data: ArticleCreateDto) {
    return createArticle(data, this.drizzle.db);
  }

  async updateArticle(id: string, data: ArticleUpdateDto) {
    return updateArticle(id, data, this.drizzle.db);
  }

  // test
  // todo: error if category already set does not occurr any more. errorhandling not working
  // todo: check how to disable the "not all paths error of eslint"
  // todo: adjust all of this in the global-interceptor, not here!
  // match errors there and to funy stuff!
  // need internet!
  async setArticleCategory(data: { articleId: string; categoryId: string }) {
    return setArticleCategory(data, this.drizzle.db);
    //   try {
    //     const res = await queries.setArticleCategory(this.drizzle.db, data);
    //     if (res) {
    //       return res;
    //     }
    //   } catch (err) {
    //     // todo: which is the error type from drizzle?

    //     console.error(err);
    //     // how can i set the error status?
    //     throw new BadRequestException();
    //   }
  }
}
