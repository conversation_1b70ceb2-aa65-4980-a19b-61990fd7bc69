@apiBaseUrl = http://localhost:6035

# get the token by running echo $GITHUB_TOKEN in a codespace terminal for github workspace
@ghuToken = ****************************************

### Get Article 
# @name getArticle
GET {{apiBaseUrl}}/api/article
Content-Type: application/json
# X-Github-Token: {{ghuToken}}

### List all articles
# @name listArticles
POST {{apiBaseUrl}}/api/article/list
Content-Type: application/json
# X-Github-Token: {{ghuToken}}

### Create an article
# @name createArticle
POST {{apiBaseUrl}}/api/article
Content-Type: application/json
# X-Github-Token: {{ghuToken}}

{
  "name": "Article {{$randomInt 1 99}}"
}
