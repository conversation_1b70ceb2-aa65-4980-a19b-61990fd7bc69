{"name": "@turbo-rent/article-db", "private": true, "version": "0.0.0", "type": "module", "scripts": {"build": "tsc", "dev": "tsc --watch"}, "exports": {"./schema": {"types": "./src/article-schema.ts", "default": "./dist/article-schema.js"}, "./dto": {"types": "./src/article-dto.ts", "default": "./dist/article-dto.js"}}, "dependencies": {"@paralleldrive/cuid2": "^2.2.2", "@turbo-rent/db-base": "workspace:*", "drizzle-orm": "^0.43.1", "drizzle-zod": "^0.8.1", "zod": "^3.25.67"}, "devDependencies": {"@turbo-rent/eslint-config": "workspace:*", "@turbo-rent/tsconfig": "workspace:*", "eslint": "^9.18.0", "typescript": "^5.7.3"}}