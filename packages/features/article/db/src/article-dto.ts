import { defaultOmits } from '@turbo-rent/db-base/util';
import { article, articleSize, category } from './article-schema.js';
import {
  createInsertSchema,
  createSelectSchema,
  createUpdateSchema,
} from 'drizzle-zod';
import { z } from 'zod/v4';

// ======================================================================
// Article
// ======================================================================

export type Article = typeof article.$inferSelect;
export type ArticleInsert = typeof article.$inferInsert;

// to do: test refinement with options object to createInsertSchema
export const ArticleCreateSchema =
  createInsertSchema(article).omit(defaultOmits);
export type ArticleCreateDto = z.infer<typeof ArticleCreateSchema>;

export const ArticleUpdateSchema =
  createUpdateSchema(article).omit(defaultOmits);
export type ArticleUpdateDto = z.infer<typeof ArticleUpdateSchema>;

export const ArticleSizeSchema = createSelectSchema(articleSize);
export type ArticleSizeDto = z.infer<typeof ArticleSizeSchema>;

export const ArticleSize = articleSize.enumValues;
export type ArticleSize = (typeof ArticleSize)[number] | null;

// ======================================================================
// Category
// ======================================================================

export type Category = typeof category.$inferSelect;
export type CategoryInsert = typeof category.$inferInsert;

export const CategoryCreateSchema = createInsertSchema(category).omit({
  ...defaultOmits,
});
export type CategoryCreateDto = z.infer<typeof CategoryCreateSchema>;
