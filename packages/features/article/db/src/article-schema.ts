import { dateFields } from '@turbo-rent/db-base/util';
import { createId } from '@paralleldrive/cuid2';
import { pgTable, text, pgEnum, real } from 'drizzle-orm/pg-core';

export const articleSize = pgEnum('articleSize', [
  'XS',
  'S',
  'M',
  'L',
  'XL',
  'XXL',
  'XXXL',
]);

export const article = pgTable('article', {
  id: text().primaryKey().$defaultFn(createId),
  name: text(),
  size: articleSize(),
  weight: real(),
  ...dateFields,
});

// todos: rearch the drizzle snake key topic
export const category = pgTable('category', {
  id: text().primaryKey().$defaultFn(createId),
  name: text(),
  description: text(),
  ...dateFields,
});
