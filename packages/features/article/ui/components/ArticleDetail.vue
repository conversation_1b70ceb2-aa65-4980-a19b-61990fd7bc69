<script setup lang="ts">
import {
  ArticleSize,
  type ArticleCreateDto,
  ArticleCreateSchema,
} from '@turbo-rent/article-db/dto';
import type { FormSubmitEvent } from '@nuxt/ui';

const store = useArticleStore();
const route = useRoute();
const sizes = ArticleSize as (ArticleSize | null)[];

const routeParamsId = computed(() =>
  Array.isArray(route.params.id) ? route.params.id[0] : route.params.id,
);

const isNew = computed(() => routeParamsId.value === 'new');

const state = reactive<Partial<ArticleCreateDto>>({
  name: '',
  size: undefined, // Default size
  weight: undefined,
});

async function onSubmit(event: FormSubmitEvent<ArticleCreateDto>) {
  if (isNew.value) {
    // Create a new article
    await store.createArticle({
      name: event.data.name,
      size: event.data.size,
      weight: event.data.weight,
    });
  } else {
    // Update existing article
    await store.updateArticle(routeParamsId.value, {
      name: event.data.name,
      size: event.data.size,
      weight: event.data.weight,
    });
  }
  // Reset the form state
}

if (!isNew.value) {
  await store.fetchArticle(routeParamsId.value);
  state.name = store.currentArticle?.name || '';
  state.size = store.currentArticle?.size || undefined;
} else {
  store.clearCurrentArticle();
}
</script>

<template>
  <UForm
    class="flex flex-col gap-4 w-1/3"
    :schema="ArticleCreateSchema"
    :state="state"
    @submit="onSubmit"
  >
    <UFormField name="name" label="Name">
      <UInput v-model="state.name" />
    </UFormField>

    <UFormField name="size" label="Size">
      <USelect v-model="state.size" :items="sizes" />
    </UFormField>

    <UButton class="w-fit" type="submit">{{
      isNew ? 'Erstellen' : 'Aktualisieren'
    }}</UButton>
  </UForm>
</template>
