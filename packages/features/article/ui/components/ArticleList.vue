<script setup lang="ts">
import type { TableRow, TableColumn } from '@nuxt/ui';
import type { Article } from '@turbo-rent/article-db/dto';

const store = useArticleStore();
const columns: TableColumn<Article>[] = [
  {
    accessorKey: 'name',
    header: 'Name',
  },
  {
    accessorKey: 'size',
    header: 'Größe',
  },
  {
    accessorKey: 'createdAt',
    header: 'Erstellt am',
    cell: ({ row }) => {
      const value = row.getValue('createdAt') as string;
      const date = new Date(value);
      return useDateFormat(date, 'DD.MM.YYYY').value.toString();
    },
  },
];

await callOnce(
  async () => {
    await store.fetchArticles();
  },
  { mode: 'navigation' },
);

// // eslint-disable-next-line @typescript-eslint/no-unused-vars
const toArticle = (articleId: string) => {
  navigateTo(`/articles/${articleId}`);
};
function onSelect(row: TableRow<Article>, e?: Event) {
  /* If you decide to also select the column you can do this  */
  // row.toggleSelected(!row.getIsSelected());
  e?.preventDefault();
  toArticle(row.original.id);
}
</script>

<template>
  <div class="flex items-center justify-between mb-4">
    <h1 class="text-2xl font-bold">Artikel</h1>
    <UButton to="/articles/new">
      <UIcon name="i-lucide-plus" class="size-1/2" />
    </UButton>
  </div>
  <UTable :data="store.articles" :columns="columns" @select="onSelect" />
</template>
