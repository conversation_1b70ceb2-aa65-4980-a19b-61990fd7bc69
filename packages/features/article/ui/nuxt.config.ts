// https://nuxt.com/docs/api/configuration/nuxt-config
import { dirname, join } from 'node:path';
import { fileURLToPath } from 'node:url';

const currentDir = dirname(fileURLToPath(import.meta.url));
export default defineNuxtConfig({
  compatibilityDate: '2024-11-01',
  devtools: { enabled: true },
  modules: ['@nuxt/eslint', '@nuxt/ui', '@pinia/nuxt', '@vueuse/nuxt'],

  css: [join(currentDir, './assets/css/main.css')],

  imports: {
    dirs: ['**/stores'],
  },

  // vite: {
  //   optimizeDeps: {
  //     exclude: ['zod'],
  //   },
  // },

  // extends: ['../../../ui'],
  extends: ['@turbo-rent/base-ui'],
});
