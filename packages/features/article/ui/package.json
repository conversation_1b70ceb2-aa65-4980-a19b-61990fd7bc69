{"name": "@turbo-rent/article-ui", "private": true, "type": "module", "main": "./nuxt.config.ts", "scripts": {"build:layer": "nuxt build", "dev:layer": "nuxt dev --port 3002", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@nuxt/eslint": "1.3.0", "@nuxt/ui": "3.1.1", "@pinia/nuxt": "^0.11.0", "@turbo-rent/article-db": "workspace:*", "@turbo-rent/base-ui": "workspace:*", "@turbo-rent/drizzle": "workspace:*", "@vueuse/core": "^13.2.0", "@vueuse/nuxt": "^13.2.0", "eslint": "^9.25.1", "nuxt": "^3.17.5", "pinia": "^3.0.2", "typescript": "^5.7.3", "vue": "^3.5.17", "vue-router": "^4.5.1"}}