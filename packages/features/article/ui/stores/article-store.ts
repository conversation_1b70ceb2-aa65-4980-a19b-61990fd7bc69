import { defineStore } from 'pinia';
import type {
  CreateArticle,
  GetArticle,
  ListArticles,
  UpdateArticle,
} from '@turbo-rent/drizzle/dto/article';
import type {
  ArticleCreateDto,
  ArticleUpdateDto,
} from '@turbo-rent/article-db/dto';
import { useDateFormat } from '@vueuse/core';

// could also create one ref with all the state? implications of that store pattern?
// interface ArticleState {
//   articles: Article[];
//   currentArticle: Article | null;
//   loading: boolean;
//   error: string | null;
// }

export const useArticleStore = defineStore('article', () => {
  const fetch = use$fetchFactory();

  const toast = useToast();

  const articles = ref<ListArticles>([]);
  const currentArticle = ref<GetArticle | null>(null);
  const loading = ref(false);
  const error = ref<string | null>(null);

  const getArticleById = computed(() => (articleId: string) => {
    return articles.value?.find((a) => a.id === articleId);
  });

  const getArticleList = computed(() =>
    articles.value?.map((article) => ({
      ...article,
      updatedAt: article.updatedAt
        ? useDateFormat(article.updatedAt, 'DD.MM.YYYY')
        : '',
    })),
  );

  async function fetchArticles() {
    loading.value = true;
    error.value = null;
    try {
      const { data } = await useApiFetch<ListArticles>('article/list', {
        method: 'POST',
      });
      if (data.value) {
        articles.value = data.value;
      }
    } catch (err) {
      error.value = 'Failed to fetch articles';
      console.error('Error fetching articles:', err);
    } finally {
      loading.value = false;
    }
  }

  // todo: try the appoach with no try catch block by using the error returned by useFetch()
  async function fetchArticle(id: string) {
    loading.value = true;
    error.value = null;
    try {
      const { data } = await useApiFetch<GetArticle>(`article/${id}`);
      if (data.value) {
        currentArticle.value = data.value;
      }
    } catch (err) {
      error.value = 'Failed to fetch article';
      console.error('Error fetching article:', err);
    } finally {
      loading.value = false;
    }
  }

  async function createArticle(articleData: ArticleCreateDto) {
    loading.value = true;
    error.value = null;
    try {
      const res = await fetch<CreateArticle>('article', {
        method: 'POST',
        body: articleData,
      });
      if (res) {
        // since we do not really want to make two querries just to get an empty relation
        const newArticle = {
          ...res[0],
          categories: [],
        };
        currentArticle.value = newArticle;
        articles.value.push(newArticle);

        toast.add({
          title: 'Success',
          description: 'The article has been created.',
          color: 'success',
        });
      }
    } catch (err) {
      // todo: improve error handling
      error.value = 'Failed to create article';
      console.error('Error creating article:', err);
    } finally {
      loading.value = false;
    }
  }

  async function updateArticle(id: string, article: ArticleUpdateDto) {
    loading.value = true;
    error.value = null;
    try {
      const res = await fetch<UpdateArticle>(`article/${id}`, {
        method: 'PATCH',
        body: article,
      });
      if (res && res[0]) {
        const index = articles.value.findIndex((a) => a.id === id);
        if (index !== -1) {
          articles.value[index] = {
            ...articles.value[index],
            ...res[0],
          };
        }
        if (currentArticle.value?.id === id) {
          currentArticle.value = {
            ...currentArticle.value,
            ...res[0],
          };
        }
      }
    } catch (err) {
      error.value = 'Failed to update article';
      console.error('Error updating article:', err);
    } finally {
      loading.value = false;
    }
  }

  function clearCurrentArticle() {
    currentArticle.value = null;
  }

  function clearError() {
    error.value = null;
  }

  return {
    articles,
    currentArticle,
    getArticleById,
    getArticleList,
    loading,
    error,
    fetchArticle,
    fetchArticles,
    createArticle,
    updateArticle,
    clearCurrentArticle,
    clearError,
  };
});
