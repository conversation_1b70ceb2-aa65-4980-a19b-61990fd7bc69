import type { UseFetchOptions } from 'nuxt/app';

/**
 * A wrapper around `useFetch()` that prepends the API base URL to the given URL.
 * To be used fetching with ssr on opp initialization.
 */
export const useApiFetch = <T = unknown>(
  url: string,
  options?: UseFetchOptions<T>,
) => {
  const config = useRuntimeConfig();
  const apiBaseUrl = config.public.apiBaseUrl;

  return useFetch(`${apiBaseUrl}/${url}`, options);
};
