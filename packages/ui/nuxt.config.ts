import { dirname, join } from 'node:path';
import { fileURLToPath } from 'node:url';

import tailwindcss from '@tailwindcss/vite';

const currentDir = dirname(fileURLToPath(import.meta.url));

export default defineNuxtConfig({
  compatibilityDate: '2024-11-01',
  devtools: { enabled: true },
  modules: ['@nuxt/eslint', '@nuxt/ui'],

  css: [join(currentDir, './assets/css/main.css')],
  // could maybe be removed
  vite: {
    plugins: [tailwindcss()],
  },

  // todo: try to move this into the admin-ui package, or shall it be present in both?
  runtimeConfig: {
    // check wether those should be ignored by turbo
    apiBaseUrl:
      process.env.NUXT_SSR_API_URL ||
      `http://localhost:${process.env.API_PORT}/api`,
    public: {
      // the public api url is will be
      // can this be relative ?
      apiBaseUrl:
        process.env.NUXT_PUBLIC_API_URL ||
        `http://localhost:${process.env.API_PORT}/api`,
    },
  },

  // this is needed to use the <NuxtLink> component in the admin-ui packageo
  // todo: according to claued i should exclude internal packages from optimization
});
