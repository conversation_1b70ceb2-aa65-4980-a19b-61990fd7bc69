{"name": "@turbo-rent/base-ui", "private": true, "type": "module", "main": "./nuxt.config.ts", "scripts": {"build:layer": "nuxt build", "dev:layer": "nuxt dev --port 3001", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@nuxt/eslint": "1.3.0", "@nuxt/ui": "3.1.1", "@tailwindcss/vite": "^4.1.11", "eslint": "^9.0.0", "nuxt": "^3.17.5", "ofetch": "^1.4.1", "tailwindcss": "^4.1.11", "typescript": "^5.6.3", "vue": "^3.5.17", "vue-router": "^4.5.1"}}