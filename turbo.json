{"$schema": "https://turborepo.com/schema.json", "concurrency": "20", "tasks": {"build": {"dependsOn": ["^build"], "env": ["NODE_ENV", "API_PORT", "DATABASE_USERNAME", "DATABASE_PASSWORD", "DATABASE_HOST", "DATABASE_PORT", "DATABASE_NAME", "DATABASE_URL", "ADMIN_UI_PORT", "DOCS_PORT"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": [".nuxt/**", ".output/**", "dist/**"]}, "lint": {}, "dev": {"dependsOn": ["^build"], "cache": false, "persistent": true}}}